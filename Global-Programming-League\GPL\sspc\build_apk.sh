#!/bin/bash

echo "Building Global Programming League APK..."
echo

echo "Step 1: Cleaning previous builds..."
flutter clean

echo "Step 2: Getting dependencies..."
flutter pub get

echo "Step 3: Building APK for release..."
flutter build apk --release

echo
echo "Build completed!"
echo "APK location: build/app/outputs/flutter-apk/app-release.apk"
echo

echo "Step 4: Building App Bundle (optional)..."
flutter build appbundle --release

echo
echo "App Bundle location: build/app/outputs/bundle/release/app-release.aab"
echo

read -p "Press any key to continue..."
