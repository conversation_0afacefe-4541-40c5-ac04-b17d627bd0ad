{"name": "gpl-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "build:analyze": "ANALYZE=true next build", "export": "next export", "deploy": "vercel --prod", "preview": "vercel"}, "dependencies": {"autoprefixer": "^10.4.21", "framer-motion": "^12.23.9", "lucide-react": "^0.526.0", "next": "15.4.4", "postcss": "^8.5.6", "qrcode": "^1.5.4", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "typescript": "^5"}}