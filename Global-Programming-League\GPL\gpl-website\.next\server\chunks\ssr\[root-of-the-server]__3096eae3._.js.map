{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/components/HeroSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/HeroSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/HeroSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/components/HeroSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/HeroSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/HeroSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/components/FeaturesSection.tsx"], "sourcesContent": ["import { Trophy, Users, Code, Target, Clock, Award, Zap, Globe } from 'lucide-react';\n\nconst FeaturesSection = () => {\n  const features = [\n    {\n      icon: Trophy,\n      title: 'Competitive Tournaments',\n      description: 'Participate in regular coding tournaments with programmers from around the world. Test your skills and climb the global leaderboards.',\n      color: 'text-yellow-500',\n      bgColor: 'bg-yellow-50',\n    },\n    {\n      icon: Code,\n      title: 'Diverse Problem Sets',\n      description: 'Access thousands of carefully curated programming problems across multiple difficulty levels and algorithmic concepts.',\n      color: 'text-blue-500',\n      bgColor: 'bg-blue-50',\n    },\n    {\n      icon: Users,\n      title: 'Global Community',\n      description: 'Connect with like-minded developers, share solutions, and learn from the best programmers worldwide.',\n      color: 'text-green-500',\n      bgColor: 'bg-green-50',\n    },\n    {\n      icon: Target,\n      title: 'Skill Assessment',\n      description: 'Track your progress with detailed analytics and personalized recommendations to improve your coding abilities.',\n      color: 'text-purple-500',\n      bgColor: 'bg-purple-50',\n    },\n    {\n      icon: Clock,\n      title: 'Real-time Contests',\n      description: 'Experience the thrill of live coding competitions with real-time leaderboards and instant feedback.',\n      color: 'text-red-500',\n      bgColor: 'bg-red-50',\n    },\n    {\n      icon: Award,\n      title: 'Recognition System',\n      description: 'Earn badges, certificates, and recognition for your achievements. Build your programming portfolio.',\n      color: 'text-indigo-500',\n      bgColor: 'bg-indigo-50',\n    },\n    {\n      icon: Zap,\n      title: 'Fast Execution',\n      description: 'Lightning-fast code execution with support for multiple programming languages and instant results.',\n      color: 'text-orange-500',\n      bgColor: 'bg-orange-50',\n    },\n    {\n      icon: Globe,\n      title: 'Multi-Platform',\n      description: 'Access GPL from anywhere - web, mobile, or desktop. Your progress syncs across all devices.',\n      color: 'text-teal-500',\n      bgColor: 'bg-teal-50',\n    },\n  ];\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Why Choose <span className=\"gradient-text\">Global Programming League</span>?\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Discover the features that make GPL the premier destination for competitive programming enthusiasts worldwide.\n          </p>\n        </div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {features.map((feature, index) => (\n            <div\n              key={index}\n              className=\"card-hover bg-white rounded-xl p-6 shadow-lg border border-gray-100\"\n            >\n              <div className={`w-12 h-12 ${feature.bgColor} rounded-lg flex items-center justify-center mb-4`}>\n                <feature.icon className={`w-6 h-6 ${feature.color}`} />\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600 leading-relaxed\">\n                {feature.description}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg border border-gray-100 max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n              Ready to Start Your Programming Journey?\n            </h3>\n            <p className=\"text-lg text-gray-600 mb-6\">\n              Join thousands of developers who are already improving their skills with GPL.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"/tournaments\"\n                className=\"px-8 py-3 hero-gradient text-white rounded-lg font-semibold hover:opacity-90 transition-opacity duration-200\"\n              >\n                Get Started Now\n              </a>\n              <a\n                href=\"/contact\"\n                className=\"px-8 py-3 border-2 border-gray-300 text-gray-700 rounded-lg font-semibold hover:border-blue-500 hover:text-blue-600 transition-colors duration-200\"\n              >\n                Learn More\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,kBAAkB;IACtB,MAAM,WAAW;QACf;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAoD;8CACrD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;gCAAgC;;;;;;;sCAE7E,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAW,CAAC,UAAU,EAAE,QAAQ,OAAO,CAAC,iDAAiD,CAAC;8CAC7F,cAAA,8OAAC,QAAQ,IAAI;wCAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,KAAK,EAAE;;;;;;;;;;;8CAErD,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAVjB;;;;;;;;;;8BAiBX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/components/StatsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StatsSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StatsSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/components/StatsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/StatsSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/StatsSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/components/QRCodeSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/QRCodeSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/QRCodeSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/components/QRCodeSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/QRCodeSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/QRCodeSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/app/page.tsx"], "sourcesContent": ["import HeroSection from '@/components/HeroSection';\nimport FeaturesSection from '@/components/FeaturesSection';\nimport StatsSection from '@/components/StatsSection';\nimport QRCodeSection from '@/components/QRCodeSection';\n\nexport default function Home() {\n  return (\n    <div className=\"pt-16\">\n      <HeroSection />\n      <FeaturesSection />\n      <StatsSection />\n      <QRCodeSection />\n    </div>\n  );\n}\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,iIAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,qIAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,kIAAA,CAAA,UAAY;;;;;0BACb,8OAAC,mIAAA,CAAA,UAAa;;;;;;;;;;;AAGpB", "debugId": null}}]}