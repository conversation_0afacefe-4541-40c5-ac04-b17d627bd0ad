@echo off
echo Setting up Android SDK for Flutter APK build...
echo.

set ANDROID_HOME=C:\Android\sdk
set CMDLINE_TOOLS_URL=https://dl.google.com/android/repository/commandlinetools-win-11076708_latest.zip

echo Creating Android SDK directory...
if not exist "%ANDROID_HOME%" mkdir "%ANDROID_HOME%"
cd /d "%ANDROID_HOME%"

echo.
echo Downloading Android Command Line Tools...
echo Please download manually from: %CMDLINE_TOOLS_URL%
echo Extract to: %ANDROID_HOME%\cmdline-tools\latest\
echo.

echo After downloading and extracting, run these commands:
echo.
echo 1. Set environment variables:
echo    setx ANDROID_HOME "%ANDROID_HOME%"
echo    setx PATH "%%PATH%%;%%ANDROID_HOME%%\cmdline-tools\latest\bin;%%ANDROID_HOME%%\platform-tools"
echo.
echo 2. Accept licenses:
echo    "%ANDROID_HOME%\cmdline-tools\latest\bin\sdkmanager" --licenses
echo.
echo 3. Install required packages:
echo    "%ANDROID_HOME%\cmdline-tools\latest\bin\sdkmanager" "platform-tools" "platforms;android-34" "build-tools;34.0.0"
echo.
echo 4. Restart your terminal and run:
echo    flutter doctor
echo    flutter build apk --release
echo.

pause
