# Global Programming League - Deployment Guide

This guide covers deploying the Global Programming League application across three platforms:
- **Frontend**: Next.js on Vercel
- **Backend**: Node.js on Render
- **Mobile**: Flutter APK generation

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ installed
- Flutter SDK installed
- Git repository access
- Vercel account
- Render account
- MongoDB Atlas database

## 📱 1. Flutter Mobile App - APK Generation

### Step 1: Prepare the Environment
```bash
cd Global-Programming-League/GPL/sspc
flutter doctor
flutter pub get
```

### Step 2: Build APK
```bash
# For Windows
./build_apk.bat

# For Linux/Mac
chmod +x build_apk.sh
./build_apk.sh
```

### Step 3: Locate APK
- **APK Location**: `build/app/outputs/flutter-apk/app-release.apk`
- **App Bundle**: `build/app/outputs/bundle/release/app-release.aab`

### Manual Build Commands
```bash
flutter clean
flutter pub get
flutter build apk --release
flutter build appbundle --release
```

### Configuration Changes Made
- Updated `applicationId` to `com.globalprogrammingleague.gpl`
- Added necessary Android permissions
- Configured ProGuard for code optimization
- Set minimum SDK to 21, target SDK to 34

## 🖥️ 2. Node.js Backend - Render Deployment

### Step 1: Prepare Repository
```bash
cd Global-Programming-League/GPL/nodejs/server
```

### Step 2: Environment Variables
Copy `.env.example` to `.env` and configure:
```env
PORT=5500
NODE_ENV=production
MONGO_URI=your_mongodb_connection_string
JWT_SECRET=your_jwt_secret
ALLOWED_ORIGINS=https://your-frontend-domain.vercel.app
# ... other variables from .env.example
```

### Step 3: Deploy to Render
1. Connect your GitHub repository to Render
2. Create a new Web Service
3. Set build command: `npm install`
4. Set start command: `npm start`
5. Add environment variables from `.env.example`
6. Deploy

### Docker Deployment (Alternative)
```bash
docker build -t gpl-backend .
docker run -p 5500:5500 --env-file .env gpl-backend
```

### Configuration Changes Made
- Updated package.json scripts for production
- Added Dockerfile for containerization
- Configured CORS for production
- Added environment-based configuration loading
- Created render.yaml for Render-specific settings

## 🌐 3. Next.js Frontend - Vercel Deployment

### Step 1: Prepare Repository
```bash
cd Global-Programming-League/GPL/gpl-website
```

### Step 2: Environment Variables
Copy `.env.example` to `.env.local` for local development:
```env
NEXT_PUBLIC_API_URL=https://your-backend-app.onrender.com
NEXT_PUBLIC_SOCKET_URL=https://your-backend-app.onrender.com
```

### Step 3: Deploy to Vercel
1. Install Vercel CLI: `npm i -g vercel`
2. Login: `vercel login`
3. Deploy: `vercel --prod`

Or use Vercel Dashboard:
1. Connect GitHub repository
2. Import project
3. Add environment variables
4. Deploy

### Configuration Changes Made
- Updated next.config.ts for production optimization
- Added vercel.json for deployment configuration
- Updated package.json with deployment scripts
- Configured image optimization and webpack settings

## 🔧 Environment Variables Reference

### Backend (.env)
```env
# Server
PORT=5500
NODE_ENV=production

# Database
MONGO_URI=mongodb+srv://username:<EMAIL>/database

# Authentication
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# CORS
ALLOWED_ORIGINS=https://your-frontend.vercel.app,http://localhost:3000
SOCKET_CORS_ORIGIN=https://your-frontend.vercel.app
```

### Frontend (.env.local)
```env
NEXT_PUBLIC_API_URL=https://your-backend.onrender.com
NEXT_PUBLIC_SOCKET_URL=https://your-backend.onrender.com
```

## 📋 Deployment Checklist

### Before Deployment
- [ ] Update all environment variables
- [ ] Test locally with production environment variables
- [ ] Ensure MongoDB Atlas is accessible
- [ ] Configure Firebase project
- [ ] Update CORS origins

### Backend Deployment
- [ ] Deploy to Render
- [ ] Verify environment variables
- [ ] Test API endpoints
- [ ] Check logs for errors

### Frontend Deployment
- [ ] Deploy to Vercel
- [ ] Verify environment variables
- [ ] Test frontend functionality
- [ ] Check API connectivity

### Mobile App
- [ ] Generate release APK
- [ ] Test APK on device
- [ ] Verify API connectivity
- [ ] Check all features work

## 🔍 Troubleshooting

### Common Issues
1. **CORS Errors**: Update ALLOWED_ORIGINS in backend
2. **API Connection**: Verify NEXT_PUBLIC_API_URL
3. **Build Failures**: Check Node.js versions
4. **APK Issues**: Run `flutter doctor` and fix issues

### Logs
- **Render**: Check service logs in dashboard
- **Vercel**: Check function logs in dashboard
- **Flutter**: Use `flutter logs` for debugging

## 📞 Support
For deployment issues, check the logs and ensure all environment variables are correctly configured.
