{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Code, Trophy, Users, Info, Settings, ExternalLink, Mail } from 'lucide-react';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const navigation = [\n    { name: 'Home', href: '/', icon: Code },\n    { name: 'About Us', href: '/about', icon: Info },\n    { name: 'Our Services', href: '/services', icon: Settings },\n    { name: 'Tournaments', href: '/tournaments', icon: Trophy },\n    { name: 'Winners', href: '/winners', icon: Users },\n    { name: 'Contact', href: '/contact', icon: Mail },\n  ];\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 flex-shrink-0\">\n            <div className=\"w-10 h-10 hero-gradient rounded-lg flex items-center justify-center\">\n              <Code className=\"w-6 h-6 text-white\" />\n            </div>\n            <span className=\"text-xl pr-8 font-bold gradient-text whitespace-nowrap\">\n              Global Programming League\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center pr-4 space-x-6 flex-1 justify-center\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-1 pr-4 text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium whitespace-nowrap\"\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n          </nav>\n\n          {/* CTA Buttons */}\n          <div className=\"hidden lg:flex items-center space-x-4 flex-shrink-0\">\n            <Link\n              href=\"/download\"\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n            >\n              Download App\n            </Link>\n            <Link\n              href=\"http://localhost:3000\" // This will be updated to point to Flutter app\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"px-6 py-2 hero-gradient text-white rounded-lg font-medium hover:opacity-90 transition-opacity duration-200 flex items-center space-x-2\"\n            >\n              <span>Access Portal</span>\n              <ExternalLink className=\"w-4 h-4\" />\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"lg:hidden p-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors duration-200\"\n          >\n            {isMenuOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isMenuOpen && (\n        <div className=\"lg:hidden bg-white border-t border-gray-200\">\n          <div className=\"px-4 py-2 space-y-1\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n            <div className=\"pt-2 border-t border-gray-200 space-y-2\">\n              <Link\n                href=\"/download\"\n                className=\"block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Download App\n              </Link>\n              <Link\n                href=\"http://localhost:3000\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"block px-3 py-2 hero-gradient text-white rounded-lg font-medium text-center\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Access Portal\n              </Link>\n            </div>\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;YAAK,MAAM,kMAAA,CAAA,OAAI;QAAC;QACtC;YAAE,MAAM;YAAY,MAAM;YAAU,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC/C;YAAE,MAAM;YAAgB,MAAM;YAAa,MAAM,0MAAA,CAAA,WAAQ;QAAC;QAC1D;YAAE,MAAM;YAAe,MAAM;YAAgB,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC1D;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,oMAAA,CAAA,QAAK;QAAC;QACjD;YAAE,MAAM;YAAW,MAAM;YAAY,MAAM,kMAAA,CAAA,OAAI;QAAC;KACjD;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,8OAAC;oCAAK,WAAU;8CAAyD;;;;;;;;;;;;sCAM3E,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;sCAK5B,8OAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;yFAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAM/D,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,cAAc;;kDAE7B,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAM,KAAK,IAAI;;;;;;;+BANX,KAAK,IAAI;;;;;sCASlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}]}