#!/bin/bash

echo "Starting Global Programming League Development Environment..."
echo

echo "Step 1: Starting Backend Server..."
cd nodejs/server
gnome-terminal -- bash -c "npm run dev; exec bash" &
cd ../..

echo "Step 2: Starting Frontend Development Server..."
cd gpl-website
gnome-terminal -- bash -c "npm run dev; exec bash" &
cd ..

echo
echo "Development servers starting..."
echo "Backend: http://localhost:5500"
echo "Frontend: http://localhost:3000"
echo
echo "Press any key to continue..."
read -n 1
