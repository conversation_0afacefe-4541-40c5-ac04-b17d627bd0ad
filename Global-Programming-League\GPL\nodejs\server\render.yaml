services:
  - type: web
    name: gpl-backend
    env: node
    plan: free
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 5500
      - key: MONGO_URI
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: JWT_EXPIRES_IN
        value: 7d
      - key: EMAIL_HOST
        value: smtp.gmail.com
      - key: EMAIL_PORT
        value: 587
      - key: EMAIL_USER
        sync: false
      - key: EMAIL_PASS
        sync: false
      - key: FIREBASE_PROJECT_ID
        sync: false
      - key: FIREBASE_PRIVATE_KEY_ID
        sync: false
      - key: FIREBASE_PRIVATE_KEY
        sync: false
      - key: FIREBASE_CLIENT_EMAIL
        sync: false
      - key: FIREBASE_CLIENT_ID
        sync: false
      - key: FIREBASE_AUTH_URI
        value: https://accounts.google.com/o/oauth2/auth
      - key: FIREBASE_TOKEN_URI
        value: https://oauth2.googleapis.com/token
      - key: ALLOWED_ORIGINS
        sync: false
      - key: MAX_FILE_SIZE
        value: 5242880
      - key: UPLOAD_PATH
        value: ./uploads
      - key: SOCKET_CORS_ORIGIN
        sync: false
