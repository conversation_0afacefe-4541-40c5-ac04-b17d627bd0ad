{"version": 2, "name": "gpl-website", "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/(.*)", "dest": "/"}], "env": {"NEXT_PUBLIC_API_URL": "@next_public_api_url", "NEXT_PUBLIC_SOCKET_URL": "@next_public_socket_url"}, "build": {"env": {"NEXT_PUBLIC_API_URL": "@next_public_api_url", "NEXT_PUBLIC_SOCKET_URL": "@next_public_socket_url"}}, "functions": {"app/api/**/*.js": {"maxDuration": 30}}}