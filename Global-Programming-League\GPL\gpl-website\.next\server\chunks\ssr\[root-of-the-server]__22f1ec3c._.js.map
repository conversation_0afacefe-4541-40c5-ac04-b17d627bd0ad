{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { ArrowRight, Play, Trophy, Users, Code, Star } from 'lucide-react';\n\nconst HeroSection = () => {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Gradient */}\n      <div className=\"absolute inset-0 hero-gradient\"></div>\n      \n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n        }}></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Badge */}\n          <div className=\"inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-8\">\n            <Star className=\"w-4 h-4 text-yellow-300\" />\n            <span className=\"text-white text-sm font-medium\">\n              Join 50,000+ Competitive Programmers\n            </span>\n          </div>\n\n          {/* Main Heading */}\n          <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight\">\n            Master Competitive\n            <br />\n            <span className=\"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent\">\n              Programming\n            </span>\n          </h1>\n\n          {/* Subheading */}\n          <p className=\"text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed\">\n            Join the Global Programming League and compete with developers worldwide. \n            Sharpen your coding skills, participate in tournaments, and climb the leaderboards.\n          </p>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\">\n            <Link\n              href=\"/tournaments\"\n              className=\"group bg-white text-blue-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-blue-50 transition-all duration-300 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n            >\n              <span>Start Competing</span>\n              <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300\" />\n            </Link>\n            \n            <button className=\"group bg-transparent border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-blue-900 transition-all duration-300 flex items-center space-x-2\">\n              <Play className=\"w-5 h-5\" />\n              <span>Watch Demo</span>\n            </button>\n          </div>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <Users className=\"w-6 h-6 text-blue-200 mr-2\" />\n                <span className=\"text-3xl font-bold text-white\">50K+</span>\n              </div>\n              <p className=\"text-blue-200\">Active Programmers</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <Trophy className=\"w-6 h-6 text-yellow-300 mr-2\" />\n                <span className=\"text-3xl font-bold text-white\">1000+</span>\n              </div>\n              <p className=\"text-blue-200\">Tournaments Hosted</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <Code className=\"w-6 h-6 text-green-300 mr-2\" />\n                <span className=\"text-3xl font-bold text-white\">10M+</span>\n              </div>\n              <p className=\"text-blue-200\">Problems Solved</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-white/30 rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse\"></div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC,gQAAgQ,CAAC;oBACrR;;;;;;;;;;;0BAGF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;sCAMnD,8OAAC;4BAAG,WAAU;;gCAA2E;8CAEvF,8OAAC;;;;;8CACD,8OAAC;oCAAK,WAAU;8CAA+E;;;;;;;;;;;;sCAMjG,8OAAC;4BAAE,WAAU;sCAA2E;;;;;;sCAMxF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAGxB,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;sDAElD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;sDAElD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAG/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;;sDAElD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/components/StatsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Trophy, Users, Code, Globe, TrendingUp, Award } from 'lucide-react';\n\nconst StatsSection = () => {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.1 }\n    );\n\n    const element = document.getElementById('stats-section');\n    if (element) {\n      observer.observe(element);\n    }\n\n    return () => {\n      if (element) {\n        observer.unobserve(element);\n      }\n    };\n  }, []);\n\n  const stats = [\n    {\n      icon: Users,\n      value: '50,000+',\n      label: 'Active Programmers',\n      description: 'Developers from 150+ countries',\n      color: 'text-blue-500',\n      bgColor: 'bg-blue-50',\n    },\n    {\n      icon: Trophy,\n      value: '1,200+',\n      label: 'Tournaments Hosted',\n      description: 'Monthly competitive events',\n      color: 'text-yellow-500',\n      bgColor: 'bg-yellow-50',\n    },\n    {\n      icon: Code,\n      value: '10M+',\n      label: 'Problems Solved',\n      description: 'Across all difficulty levels',\n      color: 'text-green-500',\n      bgColor: 'bg-green-50',\n    },\n    {\n      icon: Globe,\n      value: '150+',\n      label: 'Countries',\n      description: 'Global programming community',\n      color: 'text-purple-500',\n      bgColor: 'bg-purple-50',\n    },\n    {\n      icon: TrendingUp,\n      value: '95%',\n      label: 'Skill Improvement',\n      description: 'Users report better coding skills',\n      color: 'text-red-500',\n      bgColor: 'bg-red-50',\n    },\n    {\n      icon: Award,\n      value: '25,000+',\n      label: 'Certificates Earned',\n      description: 'Professional recognition',\n      color: 'text-indigo-500',\n      bgColor: 'bg-indigo-50',\n    },\n  ];\n\n  return (\n    <section id=\"stats-section\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            GPL by the <span className=\"gradient-text\">Numbers</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            See how our global community of programmers is growing and achieving excellence together.\n          </p>\n        </div>\n\n        {/* Stats Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {stats.map((stat, index) => (\n            <div\n              key={index}\n              className={`card-hover bg-white rounded-xl p-8 shadow-lg border border-gray-100 text-center transform transition-all duration-700 ${\n                isVisible \n                  ? 'translate-y-0 opacity-100' \n                  : 'translate-y-8 opacity-0'\n              }`}\n              style={{ transitionDelay: `${index * 100}ms` }}\n            >\n              <div className={`w-16 h-16 ${stat.bgColor} rounded-full flex items-center justify-center mx-auto mb-4`}>\n                <stat.icon className={`w-8 h-8 ${stat.color}`} />\n              </div>\n              \n              <div className=\"mb-2\">\n                <span className=\"text-4xl md:text-5xl font-bold text-gray-900\">\n                  {stat.value}\n                </span>\n              </div>\n              \n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                {stat.label}\n              </h3>\n              \n              <p className=\"text-gray-600\">\n                {stat.description}\n              </p>\n            </div>\n          ))}\n        </div>\n\n        {/* Achievement Highlights */}\n        <div className=\"mt-20\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 md:p-12\">\n            <div className=\"text-center mb-8\">\n              <h3 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-4\">\n                Recent Achievements\n              </h3>\n              <p className=\"text-lg text-gray-600\">\n                Celebrating milestones in our programming community\n              </p>\n            </div>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                  <Trophy className=\"w-6 h-6 text-yellow-600\" />\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-1\">World Championship 2024</h4>\n                <p className=\"text-gray-600 text-sm\">Successfully hosted our largest tournament with 10,000+ participants</p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                  <Users className=\"w-6 h-6 text-green-600\" />\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-1\">50K Members Milestone</h4>\n                <p className=\"text-gray-600 text-sm\">Reached 50,000 active programmers from around the globe</p>\n              </div>\n              \n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                  <Code className=\"w-6 h-6 text-blue-600\" />\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-1\">New Problem Sets</h4>\n                <p className=\"text-gray-600 text-sm\">Added 500+ new algorithmic challenges this quarter</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default StatsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,eAAe;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC,CAAC,MAAM;YACN,IAAI,MAAM,cAAc,EAAE;gBACxB,aAAa;YACf;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,IAAI,SAAS;YACX,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,SAAS;gBACX,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ;QACZ;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO;YACP,aAAa;YACb,OAAO;YACP,SAAS;QACX;KACD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAgB,WAAU;kBACpC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAoD;8CACrD,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAE7C,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4BAEC,WAAW,CAAC,sHAAsH,EAChI,YACI,8BACA,2BACJ;4BACF,OAAO;gCAAE,iBAAiB,GAAG,QAAQ,IAAI,EAAE,CAAC;4BAAC;;8CAE7C,8OAAC;oCAAI,WAAW,CAAC,UAAU,EAAE,KAAK,OAAO,CAAC,2DAA2D,CAAC;8CACpG,cAAA,8OAAC,KAAK,IAAI;wCAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;8CAG/C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;8CAIf,8OAAC;oCAAG,WAAU;8CACX,KAAK,KAAK;;;;;;8CAGb,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;;2BAvBd;;;;;;;;;;8BA8BX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAGvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Global%20Programming/Global-Programming-League/GPL/gpl-website/src/components/QRCodeSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { Smartphone, Download, Star, ArrowRight } from 'lucide-react';\nimport QRCode from 'qrcode';\n\nconst QRCodeSection = () => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  useEffect(() => {\n    const generateQR = async () => {\n      if (canvasRef.current) {\n        try {\n          // Using ICPS Google Play Store as placeholder\n          const playStoreUrl = 'https://play.google.com/store/apps/details?id=com.facebook.katana';\n          \n          await QRCode.toCanvas(canvasRef.current, playStoreUrl, {\n            width: 200,\n            margin: 2,\n            color: {\n              dark: '#1A237E',\n              light: '#FFFFFF'\n            }\n          });\n        } catch (error) {\n          console.error('Error generating QR code:', error);\n        }\n      }\n    };\n\n    generateQR();\n  }, []);\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-slate-50 to-blue-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Content Side */}\n          <div>\n            <div className=\"inline-flex items-center space-x-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-6\">\n              <Smartphone className=\"w-4 h-4\" />\n              <span>Mobile App Available</span>\n            </div>\n            \n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              Take GPL <span className=\"gradient-text\">Anywhere</span>\n            </h2>\n            \n            <p className=\"text-xl text-gray-600 mb-8 leading-relaxed\">\n              Download our mobile app and continue your competitive programming journey on the go. \n              Practice problems, participate in contests, and stay connected with the community from anywhere.\n            </p>\n\n            {/* Features List */}\n            <div className=\"space-y-4 mb-8\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center\">\n                  <Star className=\"w-3 h-3 text-green-600\" />\n                </div>\n                <span className=\"text-gray-700\">Offline problem solving capability</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center\">\n                  <Star className=\"w-3 h-3 text-green-600\" />\n                </div>\n                <span className=\"text-gray-700\">Real-time contest notifications</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center\">\n                  <Star className=\"w-3 h-3 text-green-600\" />\n                </div>\n                <span className=\"text-gray-700\">Synchronized progress across devices</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center\">\n                  <Star className=\"w-3 h-3 text-green-600\" />\n                </div>\n                <span className=\"text-gray-700\">Dark mode for comfortable coding</span>\n              </div>\n            </div>\n\n            {/* Download Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <a\n                href=\"https://play.google.com/store/apps/details?id=com.facebook.katana\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"group flex items-center justify-center space-x-3 bg-black text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors duration-200\"\n              >\n                <Download className=\"w-5 h-5\" />\n                <div className=\"text-left\">\n                  <div className=\"text-xs\">Download on</div>\n                  <div className=\"text-sm font-bold\">Google Play</div>\n                </div>\n                <ArrowRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-200\" />\n              </a>\n              \n              <a\n                href=\"#\"\n                className=\"group flex items-center justify-center space-x-3 bg-black text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors duration-200\"\n              >\n                <Download className=\"w-5 h-5\" />\n                <div className=\"text-left\">\n                  <div className=\"text-xs\">Download on the</div>\n                  <div className=\"text-sm font-bold\">App Store</div>\n                </div>\n                <ArrowRight className=\"w-4 h-4 group-hover:translate-x-1 transition-transform duration-200\" />\n              </a>\n            </div>\n          </div>\n\n          {/* QR Code Side */}\n          <div className=\"flex justify-center lg:justify-end\">\n            <div className=\"bg-white rounded-2xl p-8 shadow-xl border border-gray-100 text-center max-w-sm\">\n              <div className=\"mb-6\">\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                  Scan to Download\n                </h3>\n                <p className=\"text-gray-600 text-sm\">\n                  Use your phone camera to scan the QR code\n                </p>\n              </div>\n              \n              <div className=\"flex justify-center mb-6\">\n                <div className=\"p-4 bg-gray-50 rounded-xl\">\n                  <canvas \n                    ref={canvasRef}\n                    className=\"max-w-full h-auto\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"text-xs text-gray-500\">\n                <p>Currently redirects to ICPS app</p>\n                <p className=\"mt-1\">GPL mobile app coming soon!</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default QRCodeSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,gBAAgB;IACpB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,IAAI,UAAU,OAAO,EAAE;gBACrB,IAAI;oBACF,8CAA8C;oBAC9C,MAAM,eAAe;oBAErB,MAAM,sIAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,UAAU,OAAO,EAAE,cAAc;wBACrD,OAAO;wBACP,QAAQ;wBACR,OAAO;4BACL,MAAM;4BACN,OAAO;wBACT;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC7C;YACF;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCAAG,WAAU;;oCAAoD;kDACvD,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAG3C,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAM1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;0CAKpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAU;;;;;;kEACzB,8OAAC;wDAAI,WAAU;kEAAoB;;;;;;;;;;;;0DAErC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAGxB,8OAAC;wCACC,MAAK;wCACL,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAU;;;;;;kEACzB,8OAAC;wDAAI,WAAU;kEAAoB;;;;;;;;;;;;0DAErC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAM5B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAGzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,KAAK;4CACL,WAAU;;;;;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDACH,8OAAC;4CAAE,WAAU;sDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpC;uCAEe", "debugId": null}}]}