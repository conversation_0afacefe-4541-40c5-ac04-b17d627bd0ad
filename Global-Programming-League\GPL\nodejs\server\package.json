{"name": "nodejs", "version": "1.0.0", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build step required for Node.js'", "test": "echo 'No tests specified'", "lint": "echo 'No linting configured'"}, "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.0", "firebase-admin": "^13.4.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.10.0", "mongoose": "^8.6.3", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.16", "otp-generator": "^4.0.1", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}