# 🚀 Global Programming League - Deployment Checklist

## Pre-Deployment Setup

### 1. Database Setup
- [ ] MongoDB Atlas cluster created
- [ ] Database user created with appropriate permissions
- [ ] IP whitelist configured (0.0.0.0/0 for production)
- [ ] Connection string obtained

### 2. Firebase Setup
- [ ] Firebase project created
- [ ] Firebase Admin SDK service account key generated
- [ ] Firebase configuration added to backend

### 3. Email Setup
- [ ] Gmail app password generated (if using Gmail)
- [ ] SMTP credentials configured

## Backend Deployment (Render)

### 1. Repository Preparation
- [ ] Code pushed to GitHub repository
- [ ] Dockerfile created and tested
- [ ] Environment variables documented

### 2. Render Service Creation
- [ ] New Web Service created on Render
- [ ] GitHub repository connected
- [ ] Build command set: `npm install`
- [ ] Start command set: `npm start`
- [ ] Environment variables added:
  - [ ] PORT=5500
  - [ ] NODE_ENV=production
  - [ ] MONGO_URI
  - [ ] JWT_SECRET
  - [ ] EMAIL_HOST, EMAIL_PORT, EMAIL_USER, EMAIL_PASS
  - [ ] FIREBASE_* variables
  - [ ] ALLOWED_ORIGINS
  - [ ] SOCKET_CORS_ORIGIN

### 3. Deployment Verification
- [ ] Service deployed successfully
- [ ] Health check endpoint responding
- [ ] Database connection working
- [ ] API endpoints accessible
- [ ] Logs show no errors

## Frontend Deployment (Vercel)

### 1. Repository Preparation
- [ ] Code pushed to GitHub repository
- [ ] vercel.json configuration created
- [ ] Environment variables documented

### 2. Vercel Project Creation
- [ ] New project created on Vercel
- [ ] GitHub repository connected
- [ ] Environment variables added:
  - [ ] NEXT_PUBLIC_API_URL (Render backend URL)
  - [ ] NEXT_PUBLIC_SOCKET_URL (Render backend URL)

### 3. Deployment Verification
- [ ] Project deployed successfully
- [ ] Frontend loads without errors
- [ ] API calls working
- [ ] Real-time features working
- [ ] All pages accessible

## Mobile App (Flutter APK)

### 1. Build Preparation
- [ ] Flutter SDK updated
- [ ] Dependencies installed (`flutter pub get`)
- [ ] Android SDK configured
- [ ] Build tools updated

### 2. APK Generation
- [ ] Release build successful (`flutter build apk --release`)
- [ ] APK file generated in `build/app/outputs/flutter-apk/`
- [ ] App bundle generated (optional)

### 3. APK Testing
- [ ] APK installed on test device
- [ ] App launches successfully
- [ ] API connectivity working
- [ ] All features functional
- [ ] No crashes or errors

## Post-Deployment Testing

### 1. Integration Testing
- [ ] Frontend can communicate with backend
- [ ] Mobile app can communicate with backend
- [ ] Real-time features working across platforms
- [ ] File uploads working
- [ ] Authentication working
- [ ] Database operations working

### 2. Performance Testing
- [ ] Backend response times acceptable
- [ ] Frontend loading times acceptable
- [ ] Mobile app performance acceptable
- [ ] No memory leaks or performance issues

### 3. Security Testing
- [ ] HTTPS enabled on all platforms
- [ ] CORS properly configured
- [ ] Authentication tokens secure
- [ ] Environment variables not exposed
- [ ] API endpoints properly secured

## Final Steps

### 1. Documentation
- [ ] Deployment URLs documented
- [ ] Environment variables backed up securely
- [ ] Access credentials stored safely
- [ ] Deployment process documented

### 2. Monitoring Setup
- [ ] Error monitoring configured
- [ ] Performance monitoring enabled
- [ ] Log aggregation set up
- [ ] Alerts configured for critical issues

### 3. Backup and Recovery
- [ ] Database backup strategy implemented
- [ ] Code repository backed up
- [ ] Environment variables backed up
- [ ] Recovery procedures documented

## URLs and Access

### Production URLs
- **Frontend**: https://your-project.vercel.app
- **Backend**: https://your-service.onrender.com
- **APK Download**: [Location of APK file]

### Admin Access
- **Vercel Dashboard**: https://vercel.com/dashboard
- **Render Dashboard**: https://dashboard.render.com
- **MongoDB Atlas**: https://cloud.mongodb.com
- **Firebase Console**: https://console.firebase.google.com

## Emergency Contacts and Procedures

### Rollback Procedures
- [ ] Frontend rollback: Revert deployment in Vercel
- [ ] Backend rollback: Redeploy previous version on Render
- [ ] Database rollback: Restore from backup

### Support Contacts
- [ ] Development team contact information
- [ ] Infrastructure team contact information
- [ ] Emergency escalation procedures

---

**Note**: This checklist should be completed in order. Each section depends on the previous sections being completed successfully.
